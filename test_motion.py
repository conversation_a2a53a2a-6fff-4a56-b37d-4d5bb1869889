# test_motion.py
"""
机器人运动测试脚本

该脚本用于安全地测试机器人的基本运动功能，包括：
- 伺服使能/关闭
- 位置读取
- 线性运动控制

重要提示：运行前请确保机器人处于安全的工作环境，
并根据实际情况修改目标位置坐标。
"""

import time
import nrc_interface as nrc
from hardware.robot_interface import RobotInterface
from config import ROBOT_IP, ROBOT_PORT


def run_motion_test():
    """
    执行一个安全的运动测试序列。
    
    测试流程：
    1. 连接机器人
    2. 使能伺服
    3. 获取当前位置
    4. 执行安全的运动序列
    5. 关闭伺服
    6. 断开连接
    """
    robot = None  # 初始化变量
    
    try:
        print("=" * 60)
        print("机器人运动控制测试 - 第二步")
        print("=" * 60)
        
        # 1. 连接机器人
        print(f"正在连接机器人 {ROBOT_IP}:{ROBOT_PORT}...")
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        print("机器人连接成功。")
        print("-" * 40)

        # 2. 使能伺服
        print("正在使能伺服电机...")
        robot.enable_servos()
        time.sleep(2)  # 等待伺服使能完成
        print("-" * 40)

        # 3. 获取并打印起始位置
        print("正在获取机器人当前位置...")
        start_pos = robot.get_current_position()
        
        if start_pos is None:
            print("错误：无法获取机器人当前位置")
            return
        
        print(f"机器人当前起始位置:")
        print(f"  X: {start_pos[0]:.2f} mm")
        print(f"  Y: {start_pos[1]:.2f} mm") 
        print(f"  Z: {start_pos[2]:.2f} mm")
        print(f"  A: {start_pos[3]:.2f} deg")
        print(f"  B: {start_pos[4]:.2f} deg")
        print(f"  C: {start_pos[5]:.2f} deg")
        print("-" * 40)

        # 4. 定义安全的目标点
        # !!! 警告: 用户必须根据现场情况修改这些坐标以确保安全 !!!
        print("定义运动目标点...")
        
        # target_pos_1 是一个安全高点（Z轴向上移动20mm）
        target_pos_1 = [
            start_pos[0], 
            start_pos[1], 
            start_pos[2] + 20,  # Z轴向上20mm
            start_pos[3], 
            start_pos[4], 
            start_pos[5]
        ]
        
        # target_pos_2 是工作区的另一个点（X轴移动50mm，保持Z轴高度）
        target_pos_2 = [
            start_pos[0] + 50,  # X轴移动50mm
            start_pos[1], 
            start_pos[2] + 20,  # 保持Z轴高度
            start_pos[3], 
            start_pos[4], 
            start_pos[5]
        ]
        
        print(f"目标点1 (安全高点): {target_pos_1}")
        print(f"目标点2 (工作点): {target_pos_2}")
        print("-" * 40)

        # 5. 执行运动序列
        print("开始执行运动序列...")
        print("注意：每次运动都是阻塞式的，程序会等待运动完成")
        print()
        
        # 移动到点1（安全高点）
        print("步骤1: 移动到安全高点...")
        success = robot.move_linear(
            target_pos=target_pos_1, 
            velocity=50,  # 50 mm/s 的安全速度
            blending_radius=1
        )
        
        if not success:
            print("错误：移动到目标点1失败")
            return
        
        print("已到达目标点1。")
        time.sleep(2)  # 停留2秒
        
        # 移动到点2（工作点）
        print("\n步骤2: 移动到工作点...")
        success = robot.move_linear(
            target_pos=target_pos_2, 
            velocity=50, 
            blending_radius=1
        )
        
        if not success:
            print("错误：移动到目标点2失败")
            return
            
        print("已到达目标点2。")
        time.sleep(2)  # 停留2秒

        # 返回到点1（安全高点）
        print("\n步骤3: 返回到安全高点...")
        success = robot.move_linear(
            target_pos=target_pos_1, 
            velocity=50, 
            blending_radius=1
        )
        
        if not success:
            print("错误：返回目标点1失败")
            return
            
        print("已返回目标点1。")
        time.sleep(2)  # 停留2秒
        
        # 返回到起始位置
        print("\n步骤4: 返回到起始位置...")
        success = robot.move_linear(
            target_pos=start_pos, 
            velocity=30,  # 更慢的速度返回起始位置
            blending_radius=1
        )
        
        if not success:
            print("错误：返回起始位置失败")
            return
            
        print("已返回起始位置。")
        print("-" * 40)
        
        print("运动序列执行完毕！")
        
        # 验证最终位置
        final_pos = robot.get_current_position()
        if final_pos:
            print(f"最终位置: {final_pos}")
            
            # 计算位置偏差
            position_error = [abs(final_pos[i] - start_pos[i]) for i in range(6)]
            print(f"位置偏差: {position_error}")

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        print("请检查：")
        print("1. 机器人是否正常连接")
        print("2. 伺服是否正常使能")
        print("3. 目标位置是否在机器人工作范围内")
        print("4. 是否存在碰撞风险")
        
    finally:
        # 6. 确保安全关闭
        if robot:
            print("-" * 40)
            print("正在安全关闭系统...")
            
            # 关闭伺服
            robot.disable_servos()
            time.sleep(1)
            
            # 断开连接
            print("断开机器人连接。")
            robot.disconnect()
            
        print("=" * 60)
        print("测试完成")
        print("=" * 60)


def main():
    """
    主函数
    """
    print("警告：运行此测试前请确保：")
    print("1. 机器人周围没有障碍物")
    print("2. 已确认目标位置的安全性")
    print("3. 有紧急停止按钮可用")
    print()
    
    # 等待用户确认
    user_input = input("确认安全条件后，输入 'yes' 继续测试: ")
    if user_input.lower() != 'yes':
        print("测试已取消")
        return
    
    run_motion_test()


if __name__ == "__main__":
    main()
