# check_robot_ports.py
"""
INEXBOT机器人端口检查脚本

该脚本用于检查机器人控制器上常用端口的连通性
"""

import socket
import sys
from config import ROBOT_IP

def check_port(ip, port, timeout=3):
    """
    检查指定IP和端口的连通性
    
    Args:
        ip (str): IP地址
        port (int): 端口号
        timeout (int): 超时时间（秒）
    
    Returns:
        bool: 连通返回True，否则返回False
    """
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except Exception as e:
        return False

def main():
    """
    主函数：检查常用的INEXBOT端口
    """
    print("=" * 60)
    print("INEXBOT机器人端口连通性检查")
    print("=" * 60)
    print(f"目标IP: {ROBOT_IP}")
    print()
    
    # 常用的INEXBOT端口
    common_ports = [
        (6001, "INEXBOT控制端口（常用）"),
        (8055, "INEXBOT备用端口1"),
        (8080, "INEXBOT Web界面"),
        (9000, "INEXBOT备用端口2"),
        (502, "Modbus TCP"),
        (22, "SSH"),
        (23, "Telnet"),
        (80, "HTTP"),
        (443, "HTTPS")
    ]
    
    print("正在检查端口连通性...")
    print("-" * 60)
    
    open_ports = []
    
    for port, description in common_ports:
        print(f"检查端口 {port:5d} ({description})...", end=" ")
        
        if check_port(ROBOT_IP, port):
            print("✅ 开放")
            open_ports.append((port, description))
        else:
            print("❌ 关闭")
    
    print("-" * 60)
    
    if open_ports:
        print(f"\n发现 {len(open_ports)} 个开放端口:")
        for port, description in open_ports:
            print(f"  端口 {port}: {description}")
        
        print(f"\n建议:")
        if any(port in [6001, 8055, 9000] for port, _ in open_ports):
            print("- 发现INEXBOT相关端口开放，请尝试使用这些端口连接")
        else:
            print("- 未发现标准INEXBOT端口，请检查机器人控制器配置")
    else:
        print("\n❌ 未发现任何开放端口")
        print("\n可能的原因:")
        print("1. 机器人控制器未开机或网络服务未启动")
        print("2. IP地址不正确")
        print("3. 网络连接问题")
        print("4. 防火墙阻止连接")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
