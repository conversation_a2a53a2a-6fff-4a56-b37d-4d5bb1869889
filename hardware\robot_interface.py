# hardware/robot_interface.py
"""
INEXBOT机械臂硬件接口模块

该模块封装了与INEXBOT机械臂的所有通信细节，
提供一个干净的硬件抽象层。
"""

import nrc_interface as nrc


class RobotInterface:
    """
    INEXBOT机械臂接口类
    
    该类封装了与机械臂的连接、断开和状态检查功能，
    为上层应用提供统一的机器人控制接口。
    """
    
    def __init__(self, ip, port):
        """
        初始化机器人接口
        
        Args:
            ip (str): 机器人的IP地址
            port (int): 机器人的通信端口
            
        Raises:
            ConnectionError: 当连接失败时抛出异常
        """
        self.ip = ip
        self.port = port
        self.socket_fd = None
        self.is_connected = False
        
        # 尝试连接机器人
        print(f"正在连接机器人 {ip}:{port}...")
        result = nrc.connect_robot(ip, str(port))
        
        # 检查连接结果
        if result < 0:
            error_msg = f"连接机器人失败！错误代码: {result}"
            print(error_msg)
            raise ConnectionError(error_msg)
        
        # 连接成功，保存套接字文件描述符
        self.socket_fd = result
        self.is_connected = True
        print(f"机器人连接成功！套接字FD: {self.socket_fd}")
    
    def disconnect(self):
        """
        安全地断开与机器人的连接
        """
        if self.socket_fd is not None and self.is_connected:
            print("正在断开机器人连接...")
            nrc.disconnect_robot(self.socket_fd)
            self.is_connected = False
            print("机器人连接已断开")
        else:
            print("机器人未连接，无需断开")
    
    def get_connection_status(self):
        """
        检查当前连接状态
        
        Returns:
            int: 连接状态值，具体含义参考INEXBOT SDK文档
        """
        if self.socket_fd is not None:
            status = nrc.get_connection_status(self.socket_fd)
            return status
        else:
            return -1  # 未连接状态
    
    def is_robot_connected(self):
        """
        检查机器人是否已连接
        
        Returns:
            bool: True表示已连接，False表示未连接
        """
        return self.is_connected and self.socket_fd is not None
    
    def get_socket_fd(self):
        """
        获取套接字文件描述符
        
        Returns:
            int: 套接字文件描述符，用于后续的机器人控制操作
        """
        return self.socket_fd
    
    def enable_servos(self):
        """
        打开机器人伺服电机

        这是所有运动指令的前提条件。
        """
        if self.socket_fd is not None:
            result = nrc.set_servo_state(self.socket_fd, 1)
            if result == 0:  # SUCCESS
                print("伺服电机已使能。")
            else:
                print(f"伺服使能失败，错误代码: {result}")
        else:
            print("错误：机器人未连接，无法使能伺服")

    def disable_servos(self):
        """
        关闭机器人伺服电机
        """
        if self.socket_fd is not None:
            result = nrc.set_servo_state(self.socket_fd, 0)
            if result == 0:  # SUCCESS
                print("伺服电机已关闭。")
            else:
                print(f"伺服关闭失败，错误代码: {result}")
        else:
            print("错误：机器人未连接，无法关闭伺服")

    def get_current_position(self):
        """
        获取机器人末端当前的笛卡尔坐标 (X, Y, Z, A, B, C)

        Returns:
            list: 包含6个浮点数的列表 [X, Y, Z, A, B, C]，如果获取失败返回None
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法获取位置")
            return None

        # 创建VectorDouble对象来接收位置数据
        pos_data = nrc.VectorDouble()

        # 调用API获取当前位置
        result = nrc.get_current_position(self.socket_fd, nrc.PosType_PType, pos_data)

        if result == 0:  # SUCCESS
            # 将VectorDouble转换为Python列表
            position = []
            for i in range(6):  # X, Y, Z, A, B, C
                if i < len(pos_data):
                    position.append(pos_data[i])
                else:
                    position.append(0.0)
            return position
        else:
            print(f"获取当前位置失败，错误代码: {result}")
            return None

    def move_linear(self, target_pos, velocity, blending_radius):
        """
        发送一个阻塞式的线性运动指令 (MoveL)

        程序会在此等待直到运动完成。

        Args:
            target_pos (list): 包含6个浮点数（X,Y,Z,A,B,C）的Python列表
            velocity (float): 运动速度 (单位: mm/s)
            blending_radius (float): 路径平滑过渡的半径/等级 (pl 参数)

        Returns:
            bool: 运动成功返回True，失败返回False
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法执行运动")
            return False

        if len(target_pos) != 6:
            print("错误：目标位置必须包含6个坐标值 (X,Y,Z,A,B,C)")
            return False

        print(f"正在执行线性移动至: {target_pos}")

        # 创建MoveCmd对象
        move_cmd = nrc.MoveCmd()

        # 创建VectorDouble对象并填充目标位置
        target_vector = nrc.VectorDouble()
        for pos in target_pos:
            target_vector.push_back(float(pos))

        # 填充MoveCmd对象的属性
        move_cmd.targetPosValue = target_vector
        move_cmd.velocity = float(velocity)
        move_cmd.pl = float(blending_radius)
        move_cmd.toolNum = 1  # 默认工具
        move_cmd.userNum = 1  # 默认坐标系

        # 执行线性运动
        result = nrc.robot_movel(self.socket_fd, move_cmd)

        if result == 0:  # SUCCESS
            print("线性运动执行成功")
            return True
        else:
            print(f"线性运动执行失败，错误代码: {result}")
            return False

    def __del__(self):
        """
        析构函数，确保连接被正确关闭
        """
        self.disconnect()
