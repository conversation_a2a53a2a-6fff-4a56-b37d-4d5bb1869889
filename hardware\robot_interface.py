# hardware/robot_interface.py
"""
INEXBOT机械臂硬件接口模块

该模块封装了与INEXBOT机械臂的所有通信细节，
提供一个干净的硬件抽象层。
"""

import nrc_interface as nrc


class RobotInterface:
    """
    INEXBOT机械臂接口类
    
    该类封装了与机械臂的连接、断开和状态检查功能，
    为上层应用提供统一的机器人控制接口。
    """
    
    def __init__(self, ip, port):
        """
        初始化机器人接口
        
        Args:
            ip (str): 机器人的IP地址
            port (int): 机器人的通信端口
            
        Raises:
            ConnectionError: 当连接失败时抛出异常
        """
        self.ip = ip
        self.port = port
        self.socket_fd = None
        self.is_connected = False
        
        # 尝试连接机器人
        print(f"正在连接机器人 {ip}:{port}...")
        result = nrc.connect_robot(ip, str(port))
        
        # 检查连接结果
        if result < 0:
            error_msg = f"连接机器人失败！错误代码: {result}"
            print(error_msg)
            raise ConnectionError(error_msg)
        
        # 连接成功，保存套接字文件描述符
        self.socket_fd = result
        self.is_connected = True
        print(f"机器人连接成功！套接字FD: {self.socket_fd}")
    
    def disconnect(self):
        """
        安全地断开与机器人的连接
        """
        if self.socket_fd is not None and self.is_connected:
            print("正在断开机器人连接...")
            nrc.disconnect_robot(self.socket_fd)
            self.is_connected = False
            print("机器人连接已断开")
        else:
            print("机器人未连接，无需断开")
    
    def get_connection_status(self):
        """
        检查当前连接状态
        
        Returns:
            int: 连接状态值，具体含义参考INEXBOT SDK文档
        """
        if self.socket_fd is not None:
            status = nrc.get_connection_status(self.socket_fd)
            return status
        else:
            return -1  # 未连接状态
    
    def is_robot_connected(self):
        """
        检查机器人是否已连接
        
        Returns:
            bool: True表示已连接，False表示未连接
        """
        return self.is_connected and self.socket_fd is not None
    
    def get_socket_fd(self):
        """
        获取套接字文件描述符
        
        Returns:
            int: 套接字文件描述符，用于后续的机器人控制操作
        """
        return self.socket_fd
    
    def __del__(self):
        """
        析构函数，确保连接被正确关闭
        """
        self.disconnect()
