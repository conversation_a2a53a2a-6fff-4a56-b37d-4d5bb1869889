# hardware/extruder_interface.py
"""
Klipper挤出机硬件接口模块

该模块封装了与Klipper/Moonraker API的所有HTTP通信细节，
提供一个干净的挤出机控制抽象层。
"""

import requests
import time
import json


class ExtruderInterface:
    """
    Klipper挤出机接口类
    
    该类封装了与Klipper/Moonraker的HTTP通信，
    为上层应用提供统一的挤出机控制接口。
    """
    
    def __init__(self, ip):
        """
        初始化挤出机接口
        
        Args:
            ip (str): Klipper主机的IP地址
        """
        self.ip = ip
        self.base_url = f"http://{ip}"
        self.timeout = 10  # HTTP请求超时时间（秒）
        
        print(f"初始化Klipper挤出机接口: {self.base_url}")
    
    def get_status(self):
        """
        检查是否能成功连接到Moonraker API
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            response = requests.get(
                f"{self.base_url}/printer/info", 
                timeout=self.timeout
            )
            return response.status_code == 200
        except requests.exceptions.RequestException as e:
            print(f"连接Klipper失败: {e}")
            return False
    
    def get_temperatures(self):
        """
        获取挤出头和热床的实时温度和目标温度
        
        Returns:
            dict: 包含温度信息的字典，失败返回None
            格式: {
                "extruder": {"actual": 50.1, "target": 210.0},
                "heater_bed": {"actual": 25.0, "target": 0.0}
            }
        """
        try:
            response = requests.get(
                f"{self.base_url}/printer/objects/query?extruder&heater_bed",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                result = data.get("result", {})
                
                # 提取温度信息
                temp_info = {}
                
                # 挤出头温度
                if "extruder" in result:
                    extruder_data = result["extruder"]
                    temp_info["extruder"] = {
                        "actual": extruder_data.get("temperature", 0.0),
                        "target": extruder_data.get("target", 0.0)
                    }
                
                # 热床温度
                if "heater_bed" in result:
                    bed_data = result["heater_bed"]
                    temp_info["heater_bed"] = {
                        "actual": bed_data.get("temperature", 0.0),
                        "target": bed_data.get("target", 0.0)
                    }
                
                return temp_info
            else:
                print(f"获取温度失败，状态码: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"获取温度时发生网络错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"解析温度数据失败: {e}")
            return None
    
    def _send_gcode(self, gcode_command):
        """
        发送G-code指令到Klipper
        
        Args:
            gcode_command (str): G-code指令
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        try:
            data = {"script": gcode_command}
            response = requests.post(
                f"{self.base_url}/printer/gcode/script",
                json=data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return True
            else:
                print(f"发送G-code失败，状态码: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"发送G-code时发生网络错误: {e}")
            return False
    
    def set_heater_temperature(self, target_temp):
        """
        设置挤出头的目标温度（非阻塞）
        
        Args:
            target_temp (float): 目标温度（摄氏度）
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        gcode = f"M104 S{target_temp}"
        print(f"设置挤出头目标温度: {target_temp}°C")
        return self._send_gcode(gcode)
    
    def wait_for_temperature(self, target_temp, timeout=180):
        """
        阻塞程序，直到挤出头达到目标温度
        
        Args:
            target_temp (float): 目标温度（摄氏度）
            timeout (int): 超时时间（秒），默认180秒
            
        Raises:
            TimeoutError: 超时未达到目标温度时抛出异常
        """
        print(f"等待挤出头达到目标温度 {target_temp}°C...")
        start_time = time.time()
        tolerance = 2.0  # 温度容差范围 ±2°C
        
        while True:
            # 检查超时
            if time.time() - start_time > timeout:
                raise TimeoutError(f"等待温度超时（{timeout}秒），未能达到目标温度 {target_temp}°C")
            
            # 获取当前温度
            temp_info = self.get_temperatures()
            if temp_info is None:
                print("无法获取温度信息，继续等待...")
                time.sleep(2)
                continue
            
            if "extruder" not in temp_info:
                print("未找到挤出头温度信息，继续等待...")
                time.sleep(2)
                continue
            
            current_temp = temp_info["extruder"]["actual"]
            target_set = temp_info["extruder"]["target"]
            
            print(f"当前温度: {current_temp:.1f}°C / {target_set:.1f}°C")
            
            # 检查是否达到目标温度
            if abs(current_temp - target_temp) <= tolerance:
                print(f"已达到目标温度！当前温度: {current_temp:.1f}°C")
                break
            
            time.sleep(2)  # 每2秒检查一次
    
    def extrude(self, amount, speed):
        """
        执行一次挤出动作
        
        Args:
            amount (float): 挤出量（mm）
            speed (float): 挤出速度（mm/min）
            
        Returns:
            bool: 挤出指令发送成功返回True，失败返回False
        """
        gcode = f"G1 E{amount} F{speed}"
        print(f"执行挤出: {amount}mm @ {speed}mm/min")
        return self._send_gcode(gcode)
    
    def turn_off_heaters(self):
        """
        关闭所有加热器
        
        Returns:
            bool: 关闭成功返回True，失败返回False
        """
        print("正在关闭所有加热器...")
        
        # 关闭挤出头加热器
        success1 = self._send_gcode("M104 S0")
        
        # 关闭热床加热器
        success2 = self._send_gcode("M140 S0")
        
        if success1 and success2:
            print("所有加热器已关闭")
            return True
        else:
            print("关闭加热器时出现错误")
            return False
