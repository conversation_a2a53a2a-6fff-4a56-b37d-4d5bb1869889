# config.py
"""
机器人3D打印控制系统配置文件

该文件包含所有硬件设备的连接配置参数，
使得我们可以轻松修改配置而无需更改核心代码。
"""

# INEXBOT 机械臂的配置
# TODO: 请用户将 "************" 替换为实际的机器人IP地址
ROBOT_IP = "************"
# TODO: 请用户确认并替换为实际的机器人通信端口
ROBOT_PORT = 6001

# Klipper 挤出机的配置 (后续步骤会用到)
# TODO: 请用户将 "************" 替换为实际运行Klipper的设备IP地址
KLIPPER_IP = "************"
# TODO: 请用户确认并替换为实际的Klipper通信端口
KLIPPER_PORT = 7125

# 系统配置
DEBUG_MODE = True  # 调试模式，启用详细日志输出
CONNECTION_TIMEOUT = 10  # 连接超时时间（秒）
RETRY_ATTEMPTS = 3  # 连接重试次数
