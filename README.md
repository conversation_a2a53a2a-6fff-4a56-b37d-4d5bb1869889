# 机器人3D打印控制系统

一个基于INEXBOT机械臂和Klipper挤出机的完整3D打印控制系统，采用三层软件架构设计。

## 📋 目录

- [项目概述](#项目概述)
- [系统架构](#系统架构)
- [功能特性](#功能特性)
- [安装配置](#安装配置)
- [快速开始](#快速开始)
- [API文档](#api文档)
- [测试指南](#测试指南)
- [安全注意事项](#安全注意事项)
- [故障排除](#故障排除)
- [项目结构](#项目结构)

## 🎯 项目概述

本项目旨在构建一个完整的机器人3D打印控制系统，将INEXBOT机械臂与Klipper固件的3D打印挤出机相结合。系统能够解析G-code指令，协调控制机械臂运动和挤出机操作，实现精确的3D打印功能。

### 核心目标

- **精确控制**：实现机械臂与挤出机的精确协调控制
- **安全可靠**：提供完整的错误处理和安全保护机制
- **易于扩展**：采用模块化设计，便于功能扩展和维护
- **实时监控**：支持实时状态监控和动态参数调整

## 🏗️ 系统架构

本系统采用三层软件架构设计：

```
┌─────────────────────────────────────────┐
│              UI层 (ui/)                 │
│        用户图形界面，交互和状态显示        │
│              (未来实现)                  │
└─────────────────────────────────────────┘
                    ↕
┌─────────────────────────────────────────┐
│           核心逻辑层 (core/)             │
│      G-code解析，运动规划，协调控制       │
│              (未来实现)                  │
└─────────────────────────────────────────┘
                    ↕
┌─────────────────────────────────────────┐
│         硬件通信层 (hardware/)           │
│    ├─ RobotInterface (机械臂控制)       │
│    └─ ExtruderInterface (挤出机控制)    │
│              (已完成)                   │
└─────────────────────────────────────────┘
```

### 当前实现状态

- ✅ **硬件通信层**：完整实现机械臂和挤出机控制接口
- 🔄 **核心逻辑层**：规划中，将实现G-code解析和协调控制
- 🔄 **UI层**：规划中，将提供图形化用户界面

## ⭐ 功能特性

### 🤖 机械臂控制 (RobotInterface)

#### 基础功能
- ✅ 连接管理和状态查询
- ✅ 伺服电机控制（使能/关闭）
- ✅ 实时位置获取（笛卡尔坐标）
- ✅ 线性运动控制（MoveL）

#### 增强功能
- ✅ 运行状态监控（停止/运行/暂停/错误/急停/准备）
- ✅ 错误处理和自动恢复
- ✅ 动态速度控制（0.1-100%）
- ✅ 关节运动控制（MoveJ）
- ✅ 伺服状态查询

### 🔥 挤出机控制 (ExtruderInterface)

#### 温度管理
- ✅ 实时温度监控（挤出头和热床）
- ✅ 目标温度设置（非阻塞）
- ✅ 温度等待功能（阻塞，带超时保护）
- ✅ 自动加热器关闭

#### 挤出控制
- ✅ 精确挤出控制（量和速度）
- ✅ G-code指令发送
- ✅ HTTP通信和错误处理

## 🚀 安装配置

### 系统要求

- Python 3.8+
- Windows/Linux操作系统
- INEXBOT机械臂控制器
- 运行Klipper/Moonraker的3D打印机

### 依赖安装

```bash
# 安装Python依赖
pip install requests

# 确保INEXBOT SDK文件存在
# - nrc_host.pyd
# - nrc_interface.py
```

### 配置设置

编辑 `config.py` 文件，设置设备IP地址：

```python
# INEXBOT 机械臂配置
ROBOT_IP = "************"    # 替换为实际机器人IP
ROBOT_PORT = 8055            # 机器人通信端口

# Klipper 挤出机配置
KLIPPER_IP = "************"  # 替换为Klipper设备IP
KLIPPER_PORT = 7125          # Moonraker端口

# 系统配置
DEBUG_MODE = True            # 调试模式
CONNECTION_TIMEOUT = 10      # 连接超时时间
RETRY_ATTEMPTS = 3           # 重试次数
```

## 🏃 快速开始

### 1. 基础连接测试

```bash
# 测试机器人连接
python main.py

# 测试挤出机连接
python test_extruder.py
```

### 2. 运动控制测试

```bash
# 测试机器人运动功能
python test_motion.py

# 测试增强功能（推荐）
python test_robot_enhanced.py
```

### 3. 代码示例

#### 机器人控制示例

```python
from hardware.robot_interface import RobotInterface
from config import ROBOT_IP, ROBOT_PORT

# 连接机器人
robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

# 使能伺服
robot.enable_servos()

# 获取当前位置
current_pos = robot.get_current_position()
print(f"当前位置: {current_pos}")

# 执行线性运动
target_pos = [100, 200, 300, 0, 0, 0]  # X,Y,Z,A,B,C
robot.move_linear(target_pos, velocity=50, blending_radius=1)

# 设置速度
robot.set_speed(75.0)  # 75%速度

# 检查状态
state = robot.get_robot_running_state()
print(f"机器人状态: {state['description']}")

# 关闭伺服并断开连接
robot.disable_servos()
robot.disconnect()
```

#### 挤出机控制示例

```python
from hardware.extruder_interface import ExtruderInterface
from config import KLIPPER_IP

# 连接挤出机
extruder = ExtruderInterface(KLIPPER_IP)

# 检查连接
if extruder.get_status():
    print("挤出机连接成功")

# 设置温度并等待
extruder.set_heater_temperature(200.0)
extruder.wait_for_temperature(200.0)

# 执行挤出
extruder.extrude(amount=5.0, speed=300)

# 关闭加热器
extruder.turn_off_heaters()
```

## 📚 API文档

### RobotInterface API

#### 连接管理

```python
# 初始化连接
robot = RobotInterface(ip, port)

# 检查连接状态
is_connected = robot.is_robot_connected()
status_code = robot.get_connection_status()

# 断开连接
robot.disconnect()
```

#### 伺服控制

```python
# 使能/关闭伺服
robot.enable_servos()
robot.disable_servos()

# 获取伺服状态
servo_state = robot.get_servo_state()
# 返回: {"enabled": bool, "status_code": int}
```

#### 状态查询

```python
# 获取运行状态
running_state = robot.get_robot_running_state()
# 返回: {"status": int, "description": str}
# 状态码: 0=停止, 1=运行, 2=暂停, 3=错误, 4=急停, 5=准备
```

#### 位置控制

```python
# 获取当前位置
position = robot.get_current_position()
# 返回: [X, Y, Z, A, B, C] (mm, deg)

# 线性运动
success = robot.move_linear(target_pos, velocity, blending_radius)

# 关节运动
success = robot.robot_movej(target_pos, velocity, blending_radius)
```

#### 速度控制

```python
# 设置速度比例 (0.1-100.0%)
robot.set_speed(75.0)

# 获取当前速度比例
current_speed = robot.get_speed()
```

#### 错误处理

```python
# 清除错误状态
success = robot.clear_error()
```

### ExtruderInterface API

#### 连接管理

```python
# 初始化连接
extruder = ExtruderInterface(ip)

# 检查连接状态
is_connected = extruder.get_status()
```

#### 温度管理

```python
# 获取温度信息
temps = extruder.get_temperatures()
# 返回: {
#   "extruder": {"actual": float, "target": float},
#   "heater_bed": {"actual": float, "target": float}
# }

# 设置目标温度（非阻塞）
extruder.set_heater_temperature(target_temp)

# 等待达到目标温度（阻塞）
extruder.wait_for_temperature(target_temp, timeout=180)

# 关闭所有加热器
extruder.turn_off_heaters()
```

#### 挤出控制

```python
# 执行挤出动作
success = extruder.extrude(amount, speed)
# amount: 挤出量(mm), speed: 速度(mm/min)

# 发送自定义G-code
success = extruder._send_gcode("G1 E5 F300")
```

## 🧪 测试指南

### 测试脚本说明

| 脚本名称 | 功能描述 | 测试内容 |
|---------|---------|---------|
| `main.py` | 基础连接测试 | 机器人连接、状态查询 |
| `test_motion.py` | 运动控制测试 | 伺服控制、位置读取、线性运动 |
| `test_extruder.py` | 挤出机测试 | 温度控制、加热、挤出功能 |
| `test_robot_enhanced.py` | 增强功能测试 | 状态监控、错误处理、速度控制 |

### 测试执行顺序

#### 阶段1：基础功能验证
```bash
# 1. 测试机器人连接
python main.py

# 2. 测试挤出机连接
python test_extruder.py
```

#### 阶段2：运动功能测试
```bash
# 3. 测试基础运动功能
python test_motion.py

# 4. 测试增强功能（推荐）
python test_robot_enhanced.py
```

### 测试前准备

1. **硬件准备**
   - 确保机器人和挤出机正常启动
   - 检查网络连接
   - 确保工作空间安全

2. **软件准备**
   - 更新 `config.py` 中的IP地址
   - 确保所有依赖已安装
   - 检查INEXBOT SDK文件

3. **安全检查**
   - 清空机器人工作空间
   - 准备紧急停止按钮
   - 确认安全操作距离

## ⚠️ 安全注意事项

### 🚨 重要警告

- **始终保持安全距离**：操作期间人员应远离机器人工作空间
- **紧急停止准备**：确保紧急停止按钮随时可用
- **工作空间检查**：运行前清空所有障碍物
- **参数验证**：仔细检查所有运动参数和温度设置

### 🛡️ 安全措施

#### 机器人安全
- 运动前检查目标位置合理性
- 使用适当的运动速度（建议≤50mm/s）
- 设置合理的路径平滑参数
- 定期检查机器人状态

#### 挤出机安全
- 设置合适的温度范围（PLA: 190-210°C）
- 避免长时间高温加热
- 确保通风良好
- 使用耐热材料和工具

#### 软件安全
- 实现运动边界检查
- 设置超时保护机制
- 建立错误恢复流程
- 记录操作日志

## 🔧 故障排除

### 常见问题

#### 连接问题

**问题**: 机器人连接失败
```
解决方案:
1. 检查IP地址和端口配置
2. 确认机器人控制器网络连接
3. 检查防火墙设置
4. 验证INEXBOT SDK文件完整性
```

**问题**: Klipper连接失败
```
解决方案:
1. 确认Moonraker服务运行状态
2. 检查Klipper设备IP地址
3. 验证网络连通性
4. 检查Moonraker配置文件
```

#### 运动问题

**问题**: 机器人运动异常
```
解决方案:
1. 检查伺服使能状态
2. 验证目标位置合理性
3. 检查运动参数设置
4. 清除错误状态后重试
```

**问题**: 运动精度不足
```
解决方案:
1. 降低运动速度
2. 调整路径平滑参数
3. 检查机器人标定状态
4. 验证坐标系设置
```

#### 温度问题

**问题**: 加热器无法达到目标温度
```
解决方案:
1. 检查加热器硬件连接
2. 验证温度传感器状态
3. 检查电源供应
4. 调整PID参数
```

### 错误代码参考

#### 机器人错误代码
- `0`: 成功
- `-1`: 连接失败
- `-2`: 参数错误
- `-3`: 运动超限
- `-4`: 伺服未使能

#### HTTP错误代码
- `200`: 请求成功
- `404`: 端点不存在
- `500`: 服务器内部错误
- `timeout`: 请求超时

### 调试技巧

1. **启用调试模式**
   ```python
   # 在config.py中设置
   DEBUG_MODE = True
   ```

2. **查看详细日志**
   ```python
   # 添加日志输出
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

3. **分步测试**
   - 先测试连接，再测试功能
   - 使用小幅度运动验证
   - 逐步增加复杂度

## 📁 项目结构

```
Host_Computer/
├── README.md                    # 项目文档
├── config.py                    # 配置文件
├── main.py                      # 基础连接测试
├── nrc_host.pyd                 # INEXBOT SDK库文件
├── nrc_interface.py             # INEXBOT SDK接口
├── hardware/                    # 硬件通信层
│   ├── __init__.py             # 模块初始化
│   ├── robot_interface.py      # 机械臂控制接口
│   └── extruder_interface.py   # 挤出机控制接口
├── test_motion.py              # 运动控制测试
├── test_extruder.py            # 挤出机功能测试
└── test_robot_enhanced.py      # 增强功能测试
```

### 文件说明

#### 核心文件
- **`config.py`**: 系统配置文件，包含设备IP地址和系统参数
- **`main.py`**: 基础连接测试脚本，验证机器人连接功能
- **`nrc_host.pyd`**: INEXBOT官方SDK动态库
- **`nrc_interface.py`**: INEXBOT官方SDK Python接口

#### 硬件通信层
- **`hardware/__init__.py`**: 硬件模块初始化，导出主要接口类
- **`hardware/robot_interface.py`**: 机械臂控制接口，封装所有机器人操作
- **`hardware/extruder_interface.py`**: 挤出机控制接口，封装Klipper通信

#### 测试脚本
- **`test_motion.py`**: 机器人运动控制测试，验证基础运动功能
- **`test_extruder.py`**: 挤出机功能测试，验证温度控制和挤出功能
- **`test_robot_enhanced.py`**: 增强功能测试，验证状态监控和错误处理

## 🔮 未来规划

### 短期目标 (1-2个月)
- [ ] 实现G-code解析器
- [ ] 添加路径规划算法
- [ ] 实现机器人与挤出机协调控制
- [ ] 添加更多运动控制功能（圆弧运动等）

### 中期目标 (3-6个月)
- [ ] 开发图形化用户界面
- [ ] 实现实时监控和可视化
- [ ] 添加打印任务管理功能
- [ ] 支持多种G-code格式

### 长期目标 (6个月以上)
- [ ] 实现自适应打印参数调整
- [ ] 添加机器学习优化算法
- [ ] 支持多机器人协同打印
- [ ] 开发云端监控和控制功能

## 🤝 贡献指南

### 开发环境设置
1. Fork本项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

### 代码规范
- 遵循PEP 8 Python代码规范
- 添加完整的文档字符串
- 包含单元测试
- 确保代码安全性

### 测试要求
- 所有新功能必须包含测试
- 确保现有测试通过
- 添加集成测试验证

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

- **项目维护者**: [您的姓名]
- **邮箱**: [您的邮箱]
- **项目地址**: [GitHub仓库地址]

## 🙏 致谢

- INEXBOT团队提供的机械臂SDK
- Klipper项目团队
- 所有贡献者和测试用户

---

**⚠️ 免责声明**: 本软件仅供学习和研究使用。使用者应确保遵守所有相关安全规定，并对使用本软件造成的任何损失承担责任。

**📝 最后更新**: 2024年12月

---

*如果这个项目对您有帮助，请给我们一个 ⭐ Star！*
